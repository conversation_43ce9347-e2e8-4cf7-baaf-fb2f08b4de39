import {
    Bot,
    ChevronDown,
    ChevronRight,
    Copy,
    Download,
    Edit3,
    Eraser,
    FileText,
    Folder,
    Grid3X3,
    Menu,
    Minus,
    MoreHorizontal,
    RotateCcw,
    RotateCw,
    Save,
    Scissors,
    Send,
    Sidebar,
    Square,
    Type,
    X,
    ZoomIn,
    ZoomOut
} from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Footer from "../components/Footer";

export default function ASCIIMaker() {
  const [canvasWidth, setCanvasWidth] = useState(80);
  const [canvasHeight, setCanvasHeight] = useState(24);
  const [canvas, setCanvas] = useState([]);
  const [selectedTool, setSelectedTool] = useState("brush");
  const [selectedChar, setSelectedChar] = useState("█");
  const [isDrawing, setIsDrawing] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [cursorPos, setCursorPos] = useState({ x: 0, y: 0 });
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  
  // Enhanced paint tools state
  const [brushSize, setBrushSize] = useState(1);
  const [fillColor, setFillColor] = useState("█");
  const [lineStart, setLineStart] = useState(null);
  const [rectStart, setRectStart] = useState(null);
  const [isShiftPressed, setIsShiftPressed] = useState(false);
  const [clipboard, setClipboard] = useState(null);
  const [selection, setSelection] = useState(null);
  
  // Responsive sidebar state
  const [showLeftSidebar, setShowLeftSidebar] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [expandedCharSets, setExpandedCharSets] = useState({});
  const [collapsedSections, setCollapsedSections] = useState({
    tools: false,
    palette: false,
    shortcuts: false
  });

  // AI Assistant states
  const [showAI, setShowAI] = useState(false);
  const [aiProvider, setAiProvider] = useState('openai'); // 'openai' or 'anthropic'
  const [aiMessages, setAiMessages] = useState([
    {
      role: "assistant",
      content:
        "Hi! I can help you create ASCII art. Try asking me to generate art from text, or ask for tips!",
    },
  ]);
  const [aiInput, setAiInput] = useState("");
  const [aiLoading, setAiLoading] = useState(false);

  const canvasRef = useRef(null);

  // ASCII character sets
  const charSets = {
    basic: [
      "█",
      "▓",
      "▒",
      "░",
      "■",
      "□",
      "▪",
      "▫",
      "●",
      "○",
      "▬",
      "─",
      "│",
      "┌",
      "┐",
      "└",
      "┘",
    ],
    drawing: [
      "╔",
      "╗",
      "╚",
      "╝",
      "║",
      "═",
      "╠",
      "╣",
      "╦",
      "╩",
      "╬",
      "┼",
      "├",
      "┤",
      "┬",
      "┴",
    ],
    symbols: [
      "★",
      "☆",
      "♠",
      "♣",
      "♥",
      "♦",
      "♪",
      "♫",
      "☀",
      "☁",
      "☂",
      "☃",
      "⚡",
      "⭐",
      "✓",
      "✗",
    ],
    numbers: [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "+",
      "-",
      "=",
      "*",
      "/",
      "%",
    ],
    letters: [
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
    ],
  };

  // Initialize canvas
  useEffect(() => {
    const newCanvas = Array(canvasHeight)
      .fill(null)
      .map(() => Array(canvasWidth).fill(" "));
    setCanvas(newCanvas);
    addToHistory(newCanvas);
  }, [canvasWidth, canvasHeight]);

  const addToHistory = (newCanvas) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(newCanvas)));
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setCanvas(JSON.parse(JSON.stringify(history[historyIndex - 1])));
    }
  };

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setCanvas(JSON.parse(JSON.stringify(history[historyIndex + 1])));
    }
  };

  const handleCanvasClick = (row, col) => {
    switch (selectedTool) {
      case "brush":
      case "eraser":
        drawAtPosition(row, col);
        break;
      case "line":
        handleLineTool(row, col);
        break;
      case "rectangle":
        handleRectangleTool(row, col);
        break;
      case "fill":
        floodFill(row, col);
        break;
      case "eyedropper":
        pickColor(row, col);
        break;
      case "select":
        handleSelection(row, col);
        break;
    }
  };

  const drawAtPosition = (row, col) => {
    if (row < 0 || row >= canvasHeight || col < 0 || col >= canvasWidth) return;

    const newCanvas = [...canvas.map((row) => [...row])]; // Deep copy
    const char = selectedTool === "eraser" ? " " : selectedChar;

    // Draw with brush size
    for (let r = row - Math.floor(brushSize / 2); r <= row + Math.floor(brushSize / 2); r++) {
      for (let c = col - Math.floor(brushSize / 2); c <= col + Math.floor(brushSize / 2); c++) {
        if (r >= 0 && r < canvasHeight && c >= 0 && c < canvasWidth) {
          // Circular brush pattern
          const distance = Math.sqrt((r - row) ** 2 + (c - col) ** 2);
          if (distance <= brushSize / 2) {
            newCanvas[r][c] = char;
          }
        }
      }
    }

    setCanvas(newCanvas);
    if (!isDrawing) {
      addToHistory(newCanvas);
    }
  };

  // Enhanced tool functions
  const handleLineTool = (row, col) => {
    if (!lineStart) {
      setLineStart({ row, col });
    } else {
      drawLine(lineStart.row, lineStart.col, row, col);
      setLineStart(null);
    }
  };

  const drawLine = (startRow, startCol, endRow, endCol) => {
    const newCanvas = [...canvas.map((row) => [...row])];
    
    const dx = Math.abs(endCol - startCol);
    const dy = Math.abs(endRow - startRow);
    const sx = startCol < endCol ? 1 : -1;
    const sy = startRow < endRow ? 1 : -1;
    let err = dx - dy;

    let currentRow = startRow;
    let currentCol = startCol;

    while (true) {
      if (currentRow >= 0 && currentRow < canvasHeight && currentCol >= 0 && currentCol < canvasWidth) {
        newCanvas[currentRow][currentCol] = selectedChar;
      }

      if (currentRow === endRow && currentCol === endCol) break;

      const e2 = 2 * err;
      if (e2 > -dy) {
        err -= dy;
        currentCol += sx;
      }
      if (e2 < dx) {
        err += dx;
        currentRow += sy;
      }
    }

    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const handleRectangleTool = (row, col) => {
    if (!rectStart) {
      setRectStart({ row, col });
    } else {
      drawRectangle(rectStart.row, rectStart.col, row, col);
      setRectStart(null);
    }
  };

  const drawRectangle = (startRow, startCol, endRow, endCol) => {
    const newCanvas = [...canvas.map((row) => [...row])];
    
    const minRow = Math.min(startRow, endRow);
    const maxRow = Math.max(startRow, endRow);
    const minCol = Math.min(startCol, endCol);
    const maxCol = Math.max(startCol, endCol);

    // Draw rectangle outline or filled
    for (let r = minRow; r <= maxRow; r++) {
      for (let c = minCol; c <= maxCol; c++) {
        if (r >= 0 && r < canvasHeight && c >= 0 && c < canvasWidth) {
          // If shift is pressed, draw filled rectangle
          if (isShiftPressed || r === minRow || r === maxRow || c === minCol || c === maxCol) {
            newCanvas[r][c] = selectedChar;
          }
        }
      }
    }

    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const floodFill = (startRow, startCol) => {
    if (startRow < 0 || startRow >= canvasHeight || startCol < 0 || startCol >= canvasWidth) return;
    
    const newCanvas = [...canvas.map((row) => [...row])];
    const targetChar = newCanvas[startRow][startCol];
    const replacementChar = selectedChar;
    
    if (targetChar === replacementChar) return;

    const stack = [[startRow, startCol]];
    
    while (stack.length > 0) {
      const [row, col] = stack.pop();
      
      if (row < 0 || row >= canvasHeight || col < 0 || col >= canvasWidth) continue;
      if (newCanvas[row][col] !== targetChar) continue;
      
      newCanvas[row][col] = replacementChar;
      
      // Add adjacent cells to stack
      stack.push([row + 1, col], [row - 1, col], [row, col + 1], [row, col - 1]);
    }

    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const pickColor = (row, col) => {
    if (row >= 0 && row < canvasHeight && col >= 0 && col < canvasWidth) {
      const pickedChar = canvas[row][col];
      setSelectedChar(pickedChar === " " ? "█" : pickedChar);
      setSelectedTool("brush"); // Switch back to brush after picking
    }
  };

  const handleSelection = (row, col) => {
    if (!selection) {
      setSelection({ startRow: row, startCol: col, endRow: row, endCol: col });
    } else {
      setSelection({ ...selection, endRow: row, endCol: col });
    }
  };

  const copySelection = () => {
    if (!selection) return;
    
    const minRow = Math.min(selection.startRow, selection.endRow);
    const maxRow = Math.max(selection.startRow, selection.endRow);
    const minCol = Math.min(selection.startCol, selection.endCol);
    const maxCol = Math.max(selection.startCol, selection.endCol);
    
    const copied = [];
    for (let r = minRow; r <= maxRow; r++) {
      const row = [];
      for (let c = minCol; c <= maxCol; c++) {
        row.push(canvas[r] && canvas[r][c] ? canvas[r][c] : " ");
      }
      copied.push(row);
    }
    
    setClipboard(copied);
  };

  const pasteSelection = (row, col) => {
    if (!clipboard) return;
    
    const newCanvas = [...canvas.map((row) => [...row])];
    
    for (let r = 0; r < clipboard.length; r++) {
      for (let c = 0; c < clipboard[r].length; c++) {
        const targetRow = row + r;
        const targetCol = col + c;
        if (targetRow >= 0 && targetRow < canvasHeight && targetCol >= 0 && targetCol < canvasWidth) {
          if (clipboard[r][c] !== " ") {
            newCanvas[targetRow][targetCol] = clipboard[r][c];
          }
        }
      }
    }
    
    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const handleMouseDown = (row, col, e) => {
    e.preventDefault();
    
    if (e.button === 2) { // Right click
      handleRightClick(row, col, e);
      return;
    }
    
    setIsDrawing(true);
    handleCanvasClick(row, col);
  };

  const handleRightClick = (row, col, e) => {
    e.preventDefault();
    
    // Quick actions on right click
    if (selectedTool === "brush") {
      // Right click to pick color
      pickColor(row, col);
    } else if (clipboard) {
      // Right click to paste
      pasteSelection(row, col);
    }
  };

  const handleMouseEnter = (row, col) => {
    setCursorPos({ x: col, y: row });
    if (isDrawing && (selectedTool === "brush" || selectedTool === "eraser")) {
      drawAtPosition(row, col);
    }
  };

  // Responsive viewport detection
  useEffect(() => {
    const checkViewport = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      
      // Auto-hide sidebar on small screens
      if (width < 1024) {
        setShowLeftSidebar(false);
      } else {
        setShowLeftSidebar(true);
      }
    };

    checkViewport();
    window.addEventListener('resize', checkViewport);
    
    return () => window.removeEventListener('resize', checkViewport);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      setIsShiftPressed(e.shiftKey);
      
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              redo();
            } else {
              undo();
            }
            break;
          case 'c':
            if (selection) {
              e.preventDefault();
              copySelection();
            }
            break;
          case 'v':
            if (clipboard) {
              e.preventDefault();
              // Paste at cursor position or center
              pasteSelection(Math.floor(canvasHeight / 2), Math.floor(canvasWidth / 2));
            }
            break;
          case '\\':
            e.preventDefault();
            setShowLeftSidebar(!showLeftSidebar);
            break;
        }
      }
      
      // Tool shortcuts
      switch (e.key.toLowerCase()) {
        case 'b':
          setSelectedTool("brush");
          break;
        case 'e':
          setSelectedTool("eraser");
          break;
        case 'l':
          setSelectedTool("line");
          break;
        case 'r':
          setSelectedTool("rectangle");
          break;
        case 'f':
          setSelectedTool("fill");
          break;
        case 'i':
          setSelectedTool("eyedropper");
          break;
        case 's':
          if (!e.ctrlKey && !e.metaKey) {
            setSelectedTool("select");
          }
          break;
        case 'escape':
          setSelection(null);
          setLineStart(null);
          setRectStart(null);
          break;
      }
    };

    const handleKeyUp = (e) => {
      setIsShiftPressed(e.shiftKey);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [selection, clipboard, undo, redo, copySelection, pasteSelection, showLeftSidebar]);

  const handleMouseUp = (e) => {
    e.preventDefault();
    setIsDrawing(false);
  };

  const handleMouseLeave = (e) => {
    e.preventDefault();
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    const newCanvas = Array(canvasHeight)
      .fill(null)
      .map(() => Array(canvasWidth).fill(" "));
    setCanvas(newCanvas);
    addToHistory(newCanvas);
  };

  const exportAsText = () => {
    const text = canvas.map((row) => row.join("")).join("\n");
    const blob = new Blob([text], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "ascii-art.txt";
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyToClipboard = () => {
    const text = canvas.map((row) => row.join("")).join("\n");
    navigator.clipboard.writeText(text);
  };

  // Removed browser usage of @mastra/core; using server API instead

  const sendToAI = useCallback(async () => {
    if (!aiInput.trim()) return;

    setAiLoading(true);
    const userMessage = { role: "user", content: aiInput };
    setAiMessages((prev) => [...prev, userMessage]);
    const currentMessage = aiInput;
    setAiInput("");

    try {
      const res = await fetch('/api/ai-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: currentMessage,
          canvasSize: { width: canvasWidth, height: canvasHeight },
          currentCanvas: canvas,
        }),
      });
      const data = await res.json();

      const aiMessage = { role: "assistant", content: data.message || "" };
      setAiMessages((prev) => [...prev, aiMessage]);

      if (data.asciiArt) {
        const applyMessage = {
          role: "assistant",
          content: "Apply to canvas?",
          asciiArt: data.asciiArt,
          action: "apply",
        };
        setAiMessages((prev) => [...prev, applyMessage]);
      }
    } catch (error) {
      setAiMessages((prev) => [...prev, { role: "assistant", content: "Sorry, please try again." }]);
    } finally {
      setAiLoading(false);
    }
  }, [aiInput, canvasWidth, canvasHeight, canvas]);

  const applyAIArt = useCallback(
    (asciiArt) => {
      const lines = asciiArt.split("\n");
      const newCanvas = Array(canvasHeight)
        .fill(null)
        .map(() => Array(canvasWidth).fill(" "));

      for (let row = 0; row < Math.min(lines.length, canvasHeight); row++) {
        const line = lines[row] || "";
        for (let col = 0; col < Math.min(line.length, canvasWidth); col++) {
          newCanvas[row][col] = line[col] || " ";
        }
      }

      setCanvas(newCanvas);
      addToHistory(newCanvas);
      setAiMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "Applied to canvas! Feel free to edit further or ask for more help.",
        },
      ]);
    },
    [canvasHeight, canvasWidth, addToHistory],
  );

  const tools = [
    { id: "brush", icon: Edit3, label: "Brush", shortcut: "B" },
    { id: "eraser", icon: Eraser, label: "Eraser", shortcut: "E" },
    { id: "line", icon: Minus, label: "Line", shortcut: "L" },
    { id: "rectangle", icon: Square, label: "Rectangle", shortcut: "R" },
    { id: "fill", icon: () => <div className="w-4 h-4 border border-current rounded-sm bg-current"></div>, label: "Fill", shortcut: "F" },
    { id: "eyedropper", icon: () => <div className="w-4 h-4 border-2 border-current rounded-full relative"><div className="absolute inset-1 bg-current rounded-full"></div></div>, label: "Eyedropper", shortcut: "I" },
    { id: "select", icon: () => <div className="w-4 h-4 border-2 border-current border-dashed"></div>, label: "Select", shortcut: "S" },
    { id: "text", icon: Type, label: "Text", shortcut: "T" },
  ];

  return (
    <div className="min-h-screen bg-[#0a0a0a] text-[#00ff00] font-mono relative flex flex-col">
      {/* Top Toolbar */}
      <div className="border-b border-[#333333] bg-[#1a1a1a] px-3 md:px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Sidebar Toggle */}
            <button
              onClick={() => setShowLeftSidebar(!showLeftSidebar)}
              className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors lg:hidden"
              title="Toggle Sidebar (Ctrl+\)"
            >
              <Menu size={16} />
            </button>

            <div className="flex items-center space-x-2">
              <div className="text-[#00ff00] font-bold text-lg">ASCII</div>
              <div className="text-[#666666] hidden sm:block">MAKER</div>
            </div>

            <div className="h-6 w-px bg-[#333333] hidden md:block"></div>

            <div className="flex items-center space-x-1 md:space-x-2">
              <button
                onClick={clearCanvas}
                className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors"
              >
                <FileText size={14} />
                <span className="hidden sm:inline">Clear</span>
              </button>

              <button className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors">
                <Folder size={14} />
                <span className="hidden sm:inline">Open</span>
              </button>

              <button className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors">
                <Save size={14} />
                <span className="hidden sm:inline">Save</span>
              </button>

              <button
                onClick={exportAsText}
                className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors"
              >
                <Download size={14} />
                <span className="hidden sm:inline">Export</span>
              </button>
            </div>

            <div className="h-6 w-px bg-[#333333] hidden md:block"></div>

            <div className="flex items-center space-x-1 md:space-x-2">
              <button
                onClick={undo}
                disabled={historyIndex <= 0}
                className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] disabled:bg-[#1a1a1a] disabled:text-[#555555] border border-[#444444] rounded transition-colors"
                title="Undo (Ctrl+Z)"
              >
                <RotateCcw size={14} />
              </button>

              <button
                onClick={redo}
                disabled={historyIndex >= history.length - 1}
                className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] disabled:bg-[#1a1a1a] disabled:text-[#555555] border border-[#444444] rounded transition-colors"
                title="Redo (Ctrl+Shift+Z)"
              >
                <RotateCw size={14} />
              </button>
            </div>

            <div className="h-6 w-px bg-[#333333] hidden lg:block"></div>

            <div className="flex items-center space-x-1 md:space-x-2 overflow-hidden">
              <button
                onClick={copyToClipboard}
                className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors"
              >
                <Copy size={14} />
                <span className="hidden lg:inline">Copy All</span>
              </button>
              
              {selection && (
                <button
                  onClick={copySelection}
                  className="px-2 md:px-3 py-2 bg-[#00ff00] text-[#000000] hover:bg-[#00cc00] border border-[#00ff00] rounded text-xs flex items-center space-x-1 md:space-x-2 transition-colors"
                >
                  <Scissors size={14} />
                  <span className="hidden lg:inline">Copy Selection</span>
                </button>
              )}
              
              {clipboard && (
                <button
                  onClick={() => pasteSelection(Math.floor(canvasHeight / 2), Math.floor(canvasWidth / 2))}
                  className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors"
                >
                  <span>📋</span>
                  <span className="hidden lg:inline">Paste</span>
                </button>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Sidebar Toggle for larger screens */}
            <button
              onClick={() => setShowLeftSidebar(!showLeftSidebar)}
              className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors hidden lg:block"
              title="Toggle Sidebar (Ctrl+\)"
            >
              <Sidebar size={16} />
            </button>

            {/* AI Assistant Toggle */}
            <button
              onClick={() => setShowAI(!showAI)}
              className={`p-2 border border-[#444444] rounded transition-all relative ${
                showAI
                  ? "bg-[#007acc] text-white border-[#007acc] shadow-md"
                  : "bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] text-[#cccccc] hover:border-[#666666]"
              }`}
              title="AI Assistant"
            >
              <Bot size={16} />
              {showAI && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-[#007acc] rounded-full ring-2 ring-[#1a1a1a]"></div>
              )}
            </button>

            <div className="h-6 w-px bg-[#333333] hidden md:block"></div>

            <div className="flex items-center space-x-1 md:space-x-2">
              <button
                onClick={() => setShowGrid(!showGrid)}
                className={`p-2 border border-[#444444] rounded transition-colors ${
                  showGrid
                    ? "bg-[#00ff00] text-[#000000]"
                    : "bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] text-[#cccccc]"
                }`}
                title="Toggle Grid"
              >
                <Grid3X3 size={14} />
              </button>

              <button
                onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}
                className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors"
                title="Zoom Out"
              >
                <ZoomOut size={14} />
              </button>

              <span className="text-xs text-[#888888] min-w-[2rem] md:min-w-[3rem] text-center">
                {Math.round(zoom * 100)}%
              </span>

              <button
                onClick={() => setZoom(Math.min(3, zoom + 0.25))}
                className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors"
                title="Zoom In"
              >
                <ZoomIn size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 min-h-0 relative">
        {/* Left Panel - Tools & Character Palette */}
        <div className={`${
          showLeftSidebar ? 'translate-x-0' : '-translate-x-full'
        } fixed lg:relative lg:translate-x-0 z-30 w-80 lg:w-80 xl:w-96 border-r border-[#333333] bg-[#1a1a1a] flex flex-col overflow-hidden flex-shrink-0 h-full max-h-screen lg:max-h-full transition-transform duration-300 ease-in-out`}>
          {/* Fixed Header */}
          <div className="p-3 pb-0 flex-shrink-0">
            <div className="flex items-center justify-between mb-3 lg:hidden">
              <h3 className="text-sm font-bold text-[#00ff00]">TOOLS & PALETTE</h3>
              <button onClick={() => setShowLeftSidebar(false)} className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors">
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Fixed Priority Content */}
          <div className="px-3 flex-shrink-0">
            <div className="mb-4">
            <button onClick={() => setCollapsedSections(prev => ({ ...prev, tools: !prev.tools }))} className="flex items-center justify-between w-full text-xs font-bold text-[#00ff00] mb-2 hidden lg:flex hover:text-[#00cc00] transition-colors">
              <span>TOOLS</span>
              {collapsedSections.tools ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
            </button>
            <h3 className="text-xs font-bold text-[#00ff00] mb-2 lg:hidden">TOOLS</h3>
            {!collapsedSections.tools && (
            <div className="grid grid-cols-2 xl:grid-cols-2 gap-1.5">
              {tools.map((tool) => {
                const Icon = tool.icon;
                return (
                  <button
                    key={tool.id}
                    onClick={() => setSelectedTool(tool.id)}
                    title={`${tool.label} (${tool.shortcut})`}
                    className={`p-2 border rounded text-xs flex flex-col items-center space-y-0.5 transition-colors relative ${
                      selectedTool === tool.id
                        ? "bg-[#00ff00] text-[#000000] border-[#00ff00]"
                        : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a] active:bg-[#4a4a4a]"
                    }`}
                  >
                    {typeof Icon === 'function' ? <Icon /> : <Icon size={16} />}
                    <span>{tool.label}</span>
                    <span className="absolute top-1 right-1 text-[10px] opacity-60">
                      {tool.shortcut}
                    </span>
                  </button>
                );
              })}
            </div>
            )}
            
            {/* Tool Options */}
            {selectedTool === "brush" && (
              <div className="mt-3 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-1">Brush Size</div>
                <input type="range" min="1" max="10" value={brushSize} onChange={(e) => setBrushSize(parseInt(e.target.value))} className="w-full" />
                <div className="text-xs text-[#666666] mt-1">Size: {brushSize}</div>
              </div>
            )}
            
            {(lineStart || rectStart) && (
              <div className="mt-2 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-1">{lineStart ? "Click to finish line" : "Click to finish rectangle"}</div>
                <div className="text-xs text-[#666666] mb-2">Hold Shift for {rectStart ? "filled rectangle" : "constrained line"}</div>
                <button onClick={() => { setLineStart(null); setRectStart(null); }} className="px-2 py-1 bg-[#444444] hover:bg-[#555555] text-xs rounded">
                  Cancel (Esc)
                </button>
              </div>
            )}
            
            {selection && (
              <div className="mt-2 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-2">Selection Active</div>
                <div className="flex gap-1">
                  <button onClick={copySelection} className="px-2 py-1 bg-[#444444] hover:bg-[#555555] text-xs rounded">Copy</button>
                  <button onClick={() => setSelection(null)} className="px-2 py-1 bg-[#444444] hover:bg-[#555555] text-xs rounded">Clear</button>
                </div>
              </div>
            )}
            
            {clipboard && (
              <div className="mt-2 p-2 bg-[#2a2a2a] border border-[#444444] rounded">
                <div className="text-xs text-[#888888] mb-1">Clipboard: {clipboard.length}×{clipboard[0]?.length || 0}</div>
                <button onClick={() => pasteSelection(Math.floor(canvasHeight / 2), Math.floor(canvasWidth / 2))} className="px-2 py-1 bg-[#00ff00] text-[#000000] hover:bg-[#00cc00] text-xs rounded">Paste</button>
              </div>
            )}
          </div>

          {/* Quick Colors */}
          <div className="mb-4">
            <button onClick={() => setCollapsedSections(prev => ({ ...prev, palette: !prev.palette }))} className="flex items-center justify-between w-full text-xs font-bold text-[#00ff00] mb-2 hover:text-[#00cc00] transition-colors">
              <span>QUICK PALETTE</span>
              {collapsedSections.palette ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
            </button>
            {!collapsedSections.palette && (
            <div className="grid grid-cols-8 gap-1 mb-3">
              {["█", "▓", "▒", "░", "■", "□", "●", "○"].map((char, index) => (
                <button
                  key={`quick-${index}`}
                  onClick={() => setSelectedChar(char)}
                  className={`w-7 h-7 border rounded text-center flex items-center justify-center transition-colors ${
                    selectedChar === char
                      ? "bg-[#00ff00] text-[#000000] border-[#00ff00]"
                      : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a] active:bg-[#4a4a4a]"
                  }`}
                >
                  {char}
                </button>
              ))}
            </div>
            )}
          </div>


            {/* Canvas Settings - Fixed/Always Visible */}
            <div className="mb-4 bg-[#1a1a1a] border border-[#00ff00]/30 rounded-lg p-3">
              <h3 className="text-xs font-bold text-[#00ff00] mb-3 flex items-center">
                <span className="mr-2">🎨</span>CANVAS SETTINGS
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-xs text-[#888888] mb-1 block">Width</label>
                  <input type="number" value={canvasWidth} onChange={(e) => setCanvasWidth(Math.max(1, Math.min(200, parseInt(e.target.value) || 80)))} className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs" min="1" max="200" />
                </div>
                <div>
                  <label className="text-xs text-[#888888] mb-1 block">Height</label>
                  <input type="number" value={canvasHeight} onChange={(e) => setCanvasHeight(Math.max(1, Math.min(100, parseInt(e.target.value) || 24)))} className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs" min="1" max="100" />
                </div>
                
                {/* Additional Canvas Options */}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-xs text-[#888888] mb-1 block">Preset</label>
                    <select className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs" onChange={(e) => {
                      const [w, h] = e.target.value.split('x').map(Number);
                      if (w && h) { setCanvasWidth(w); setCanvasHeight(h); }
                    }}>
                      <option value="">Custom</option>
                      <option value="80x24">Terminal (80×24)</option>
                      <option value="120x30">Large (120×30)</option>
                      <option value="40x20">Small (40×20)</option>
                      <option value="100x50">Banner (100×50)</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-xs text-[#888888] mb-1 block">Aspect</label>
                    <button onClick={() => {
                      const aspectRatio = canvasWidth / canvasHeight;
                      const newHeight = Math.round(canvasWidth / aspectRatio);
                      setCanvasHeight(Math.max(1, Math.min(100, newHeight)));
                    }} className="w-full px-2 py-1 bg-[#444444] hover:bg-[#555555] border border-[#666666] rounded text-[#cccccc] text-xs">
                      Lock Ratio
                    </button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-2 border-t border-[#333333]">
                  <span className="text-xs text-[#888888]">Size: {canvasWidth}×{canvasHeight}</span>
                  <span className="text-xs text-[#666666]">{canvasWidth * canvasHeight} cells</span>
                </div>
              </div>
            </div>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 min-h-0 overflow-y-auto px-3">
            {/* Character Sets */}
            <div className="mb-4">
              <h3 className="text-xs font-bold text-[#00ff00] mb-2">CHARACTER SETS</h3>
              {Object.entries(charSets).map(([setName, chars]) => (
                <div key={setName} className="mb-3">
                  <button onClick={() => setExpandedCharSets(prev => ({ ...prev, [setName]: !prev[setName] }))} className="flex items-center justify-between w-full text-xs text-[#888888] mb-1.5 uppercase hover:text-[#cccccc] transition-colors">
                    <span>{setName}</span>
                    {expandedCharSets[setName] ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
                  </button>
                  {(expandedCharSets[setName] !== false) && (
                    <div className="grid grid-cols-8 gap-1">
                      {chars.map((char, index) => (
                        <button key={`${setName}-${index}`} onClick={() => setSelectedChar(char)} className={`w-6 h-6 border rounded text-center flex items-center justify-center transition-colors text-xs ${selectedChar === char ? "bg-[#00ff00] text-[#000000] border-[#00ff00]" : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a] active:bg-[#4a4a4a]"}`}>
                          {char}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Keyboard Shortcuts */}
            <div>
            <button onClick={() => setCollapsedSections(prev => ({ ...prev, shortcuts: !prev.shortcuts }))} className="flex items-center justify-between w-full text-xs font-bold text-[#00ff00] mb-2 hover:text-[#00cc00] transition-colors">
              <span>SHORTCUTS</span>
              {collapsedSections.shortcuts ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
            </button>
            {!collapsedSections.shortcuts && (
            <div className="space-y-0.5 text-xs text-[#888888]">
              <div className="flex justify-between">
                <span>Brush</span>
                <span className="text-[#666666]">B</span>
              </div>
              <div className="flex justify-between">
                <span>Eraser</span>
                <span className="text-[#666666]">E</span>
              </div>
              <div className="flex justify-between">
                <span>Line</span>
                <span className="text-[#666666]">L</span>
              </div>
              <div className="flex justify-between">
                <span>Rectangle</span>
                <span className="text-[#666666]">R</span>
              </div>
              <div className="flex justify-between">
                <span>Fill</span>
                <span className="text-[#666666]">F</span>
              </div>
              <div className="flex justify-between">
                <span>Eyedropper</span>
                <span className="text-[#666666]">I</span>
              </div>
              <div className="flex justify-between">
                <span>Select</span>
                <span className="text-[#666666]">S</span>
              </div>
              <div className="border-t border-[#333333] my-1"></div>
              <div className="flex justify-between">
                <span>Undo</span>
                <span className="text-[#666666]">Ctrl+Z</span>
              </div>
              <div className="flex justify-between">
                <span>Redo</span>
                <span className="text-[#666666]">Ctrl+Shift+Z</span>
              </div>
              <div className="flex justify-between">
                <span>Copy</span>
                <span className="text-[#666666]">Ctrl+C</span>
              </div>
              <div className="flex justify-between">
                <span>Paste</span>
                <span className="text-[#666666]">Ctrl+V</span>
              </div>
              <div className="flex justify-between">
                <span>Cancel</span>
                <span className="text-[#666666]">Esc</span>
              </div>
              <div className="border-t border-[#333333] my-1"></div>
              <div className="text-[#666666] text-[10px]">
                Right-click: Pick color or paste
              </div>
              <div className="text-[#666666] text-[10px]">Shift+Rectangle: Fill mode</div>
            </div>
            )}
          </div>
          </div>
        </div>

        {/* Overlay for mobile sidebar */}
        {showLeftSidebar && (
          <div 
            className="fixed inset-0 bg-black/50 z-20 lg:hidden"
            onClick={() => setShowLeftSidebar(false)}
          />
        )}

        {/* Mobile Floating Toolbar */}
        {!showLeftSidebar && (
          <div className="fixed bottom-4 left-4 right-4 z-30 lg:hidden">
            <div className="bg-[#1a1a1a] border border-[#333333] rounded-lg p-2 shadow-lg">
              <div className="flex items-center justify-center space-x-2 overflow-x-auto">
                {tools.slice(0, 6).map((tool) => {
                  const Icon = tool.icon;
                  return (
                    <button
                      key={tool.id}
                      onClick={() => setSelectedTool(tool.id)}
                      className={`p-2 border rounded transition-colors flex-shrink-0 ${
                        selectedTool === tool.id
                          ? "bg-[#00ff00] text-[#000000] border-[#00ff00]"
                          : "bg-[#2a2a2a] text-[#cccccc] border-[#444444] hover:bg-[#3a3a3a]"
                      }`}
                    >
                      {typeof Icon === 'function' ? <Icon /> : <Icon size={16} />}
                    </button>
                  );
                })}
                <button
                  onClick={() => setShowLeftSidebar(true)}
                  className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] border border-[#444444] rounded text-[#cccccc] transition-colors flex-shrink-0"
                >
                  <MoreHorizontal size={16} />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Center Panel - Full Screen Canvas */}
        <div className="flex-1 bg-[#0a0a0a] flex flex-col min-w-0 relative overflow-hidden">
          {/* Floating Status Bar */}
          <div className="absolute top-4 left-4 z-10 bg-[#1a1a1a]/90 backdrop-blur-sm border border-[#333333] rounded-lg px-3 py-2 flex items-center space-x-4 text-xs text-[#888888]">
            <span className="text-[#00ff00] font-bold">Tool: {tools.find(t => t.id === selectedTool)?.label}</span>
            <span>Pos: {cursorPos.x}, {cursorPos.y}</span>
            <span>Size: {canvasWidth}×{canvasHeight}</span>
            {selectedTool === "brush" && <span>Brush: {brushSize}px</span>}
          </div>

          {/* Full Screen Canvas Area */}
          <div className="flex-1 w-full h-full overflow-hidden flex items-center justify-center">
            <div
              className="flex items-center justify-center"
              style={{
                transform: `scale(${zoom})`,
                transformOrigin: "center",
                minWidth: "100%",
                minHeight: "100%"
              }}
            >
              <div
                ref={canvasRef}
                className="font-mono leading-none select-none bg-[#0a0a0a] border border-[#333333] shadow-2xl"
                style={{
                  fontSize: "14px",
                  lineHeight: "16px",
                  letterSpacing: "0",
                  width: `${canvasWidth * 8}px`,
                  height: `${canvasHeight * 16}px`,
                  maxWidth: "100vw",
                  maxHeight: "100vh"
                }}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseUp}
                  onContextMenu={(e) => e.preventDefault()}
                  onWheel={(e) => e.preventDefault()}
                >
                  {canvas.map((row, rowIndex) => (
                    <div key={rowIndex} className="flex">
                      {row.map((cell, colIndex) => {
                        // Check if this cell is in selection
                        const isInSelection = selection && 
                          rowIndex >= Math.min(selection.startRow, selection.endRow) &&
                          rowIndex <= Math.max(selection.startRow, selection.endRow) &&
                          colIndex >= Math.min(selection.startCol, selection.endCol) &&
                          colIndex <= Math.max(selection.startCol, selection.endCol);
                        
                        // Check if this is a line/rect preview position
                        const isPreview = (lineStart && selectedTool === "line") || 
                                         (rectStart && selectedTool === "rectangle");
                        
                        return (
                          <div
                            key={`${rowIndex}-${colIndex}`}
                            className={`w-[8px] h-[16px] flex items-center justify-center cursor-crosshair transition-colors relative ${
                              showGrid ? "border border-[#333333]/30" : ""
                            } ${
                              isInSelection ? "bg-[#00ff00]/20 border-[#00ff00]/50" : "hover:bg-[#333333]/50"
                            } ${
                              cursorPos.x === colIndex && cursorPos.y === rowIndex ? "bg-[#00ff00]/10" : ""
                            }`}
                            onMouseDown={(e) => handleMouseDown(rowIndex, colIndex, e)}
                            onMouseEnter={() => handleMouseEnter(rowIndex, colIndex)}
                          >
                            <span className="text-[#00ff00] relative z-10">{cell}</span>
                            
                            {/* Selection overlay */}
                            {isInSelection && (
                              <div className="absolute inset-0 border border-[#00ff00] pointer-events-none" />
                            )}
                            
                            {/* Tool preview indicators */}
                            {lineStart && selectedTool === "line" && rowIndex === lineStart.row && colIndex === lineStart.col && (
                              <div className="absolute inset-0 bg-[#ff0000]/50 pointer-events-none" />
                            )}
                            {rectStart && selectedTool === "rectangle" && rowIndex === rectStart.row && colIndex === rectStart.col && (
                              <div className="absolute inset-0 bg-[#ff0000]/50 pointer-events-none" />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ))}
                </div>
              </div>
            </div>
        </div>

        {/* Right Panel - AI Assistant */}
        {showAI && (
          <div className={`${
            isMobile ? 'fixed inset-0 z-40' : 'w-80 lg:w-96'
          } border-l border-[#333333] bg-[#1a1a1a] flex flex-col flex-shrink-0`}>
            {/* AI Header */}
            <div className="border-b border-[#333333] p-4 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-[#00ff00] to-[#00cc00] rounded-full flex items-center justify-center">
                  <Bot size={16} className="text-[#000000]" />
                </div>
                <div>
                  <div className="text-[#00ff00] font-bold text-sm">
                    ASCII Assistant
                  </div>
                  <div className="text-[#666666] text-xs">
                    {aiProvider === 'anthropic' ? 'Anthropic Claude 3.5' : 'OpenAI GPT-4'} via Maestra AI
                  </div>
                </div>
              </div>
              <button
                onClick={() => setShowAI(false)}
                className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* AI Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {aiMessages.map((message, index) => (
                <div
                  key={index}
                  className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.role === "user"
                        ? "bg-[#00ff00] text-[#000000]"
                        : "bg-[#2a2a2a] text-[#cccccc]"
                    }`}
                  >
                    <div className="text-sm whitespace-pre-wrap">
                      {message.content}
                    </div>
                    {message.asciiArt && (
                      <div className="mt-2">
                        <div className="bg-[#0a0a0a] p-2 rounded text-[#00ff00] font-mono text-xs overflow-x-auto">
                          {message.asciiArt}
                        </div>
                        {message.action === "apply" && (
                          <button
                            onClick={() => applyAIArt(message.asciiArt)}
                            className="mt-2 px-3 py-1 bg-[#00ff00] text-[#000000] rounded text-xs font-bold hover:bg-[#00cc00] transition-colors"
                          >
                            Apply to Canvas
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {aiLoading && (
                <div className="flex justify-start">
                  <div className="bg-[#2a2a2a] text-[#cccccc] rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin w-4 h-4 border-2 border-[#00ff00] border-t-transparent rounded-full"></div>
                      <span className="text-sm">Generating...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* AI Input */}
            <div className="border-t border-[#333333] p-4">
              {/* AI Provider Selection */}
              <div className="mb-3">
                <label className="text-xs text-[#888888] mb-1 block">AI Provider</label>
                <select 
                  value={aiProvider} 
                  onChange={(e) => setAiProvider(e.target.value)}
                  className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs"
                >
                  <option value="openai">OpenAI GPT-4</option>
                  <option value="anthropic">Anthropic Claude 3.5 Sonnet</option>
                </select>
              </div>
              
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={aiInput}
                  onChange={(e) => setAiInput(e.target.value)}
                  onKeyDown={(e) =>
                    e.key === "Enter" && !e.shiftKey && sendToAI()
                  }
                  placeholder="Ask me to create ASCII art or help you..."
                  className="flex-1 px-3 py-2 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-sm placeholder-[#666666] focus:border-[#00ff00] focus:outline-none"
                />
                <button
                  onClick={sendToAI}
                  disabled={!aiInput.trim() || aiLoading}
                  className="px-4 py-2 bg-[#00ff00] text-[#000000] rounded font-bold disabled:bg-[#666666] disabled:text-[#333333] hover:bg-[#00cc00] transition-colors"
                >
                  <Send size={16} />
                </button>
              </div>
              <div className="mt-2 text-xs text-[#666666]">
                Try: "Make a cat", "Create a heart", "Draw mountains", "Help me with shading"
                <div className="mt-1 text-[10px] text-[#555555]">
                  Requires {aiProvider === 'anthropic' ? 'ANTHROPIC_API_KEY' : 'OPENAI_API_KEY'} env variable
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}

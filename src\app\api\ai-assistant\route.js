export async function POST(request) {
  try {
    const { message, canvasSize, currentCanvas } = await request.json();

    if (!message) {
      return Response.json({ error: "Message is required" }, { status: 400 });
    }

    // Prepare context for AI
    const canvasText = currentCanvas
      ? currentCanvas.map((row) => row.join("")).join("\n")
      : "";

    const systemPrompt = `You are an expert ASCII art assistant. You help users create and edit ASCII art with precision and creativity.

Canvas size: ${canvasSize.width}x${canvasSize.height} characters
Current canvas content:
${canvasText || "(empty canvas)"}

When users ask you to generate ASCII art:
1. Create ASCII art that fits within the canvas dimensions (${canvasSize.width}x${canvasSize.height})
2. Use simple ASCII characters: █▓▒░■□▪▫●○▬─│┌┐└┘╔╗╚╝║═╠╣╦╩╬┼├┤┬┴★☆♠♣♥♦♪♫☀☁☂☃⚡⭐✓✗0-9A-Z
3. Keep designs simple and recognizable
4. Consider the limited resolution and work within those constraints
5. Return clean, properly formatted ASCII art

For general help:
- Provide tips about ASCII art techniques
- Suggest character combinations for different effects
- Help with tool usage and canvas management
- Offer creative ideas and inspiration

Respond helpfully and enthusiastically about ASCII art creation.`;

    // Basic pattern matching for common requests while AI integration is pending
    let response = {
      message:
        "I'd love to help you create ASCII art! However, to provide the best assistance, please select an AI integration first. In the meantime, here are some examples I can create:",
      asciiArt: null,
    };

    const lowerMessage = message.toLowerCase();

    // Enhanced ASCII art examples based on request
    if (lowerMessage.includes("cat")) {
      response = {
        message: "Here's a simple ASCII cat for you!",
        asciiArt: `    /\\_/\\  
   ( o.o ) 
    > ^ <  
  ~~~~~~~~~~`,
      };
    } else if (lowerMessage.includes("heart")) {
      response = {
        message: "Here's a heart in ASCII!",
        asciiArt: `  ♥♥   ♥♥  
 ♥♥♥♥ ♥♥♥♥ 
♥♥♥♥♥♥♥♥♥♥
 ♥♥♥♥♥♥♥♥♥ 
  ♥♥♥♥♥♥♥  
   ♥♥♥♥♥   
    ♥♥♥    
     ♥     `,
      };
    } else if (lowerMessage.includes("star")) {
      response = {
        message: "Here's a star pattern!",
        asciiArt: `    ★    
   ★★★   
  ★★★★★  
 ★★★★★★★ 
★★★★★★★★★
 ★★★★★★★ 
  ★★★★★  
   ★★★   
    ★    `,
      };
    } else if (
      lowerMessage.includes("house") ||
      lowerMessage.includes("home")
    ) {
      response = {
        message: "Here's a simple house!",
        asciiArt: `    /\\    
   /  \\   
  /____\\  
  |    |  
  |    |  
  | [] |  
  |____|  `,
      };
    } else if (lowerMessage.includes("tree")) {
      response = {
        message: "Here's a simple tree!",
        asciiArt: `    ★    
   ░░░   
  ░░░░░  
 ░░░░░░░ 
░░░░░░░░░
   |||   
   |||   `,
      };
    } else if (
      lowerMessage.includes("smiley") ||
      lowerMessage.includes("smile") ||
      lowerMessage.includes("face")
    ) {
      response = {
        message: "Here's a happy face!",
        asciiArt: `  ○○○○○  
 ○     ○ 
○ ●   ● ○
○       ○
○  \\_/  ○
 ○     ○ 
  ○○○○○  `,
      };
    } else if (lowerMessage.includes("flower")) {
      response = {
        message: "Here's a simple flower!",
        asciiArt: `   ●●●   
  ● ♥ ●  
   ●●●   
    |    
    |    
 ~~~~~~~~`,
      };
    } else if (lowerMessage.includes("diamond")) {
      response = {
        message: "Here's a diamond shape!",
        asciiArt: `    ♦    
   ♦♦♦   
  ♦♦♦♦♦  
 ♦♦♦♦♦♦♦ 
  ♦♦♦♦♦  
   ♦♦♦   
    ♦    `,
      };
    } else if (lowerMessage.includes("help") || lowerMessage.includes("tip")) {
      response = {
        message: `Here are some ASCII art tips:

• Use █ ▓ ▒ ░ for solid-to-transparent shading
• Try ● ○ for round shapes 
• Use ─ │ ┌ ┐ └ ┘ for box drawing
• ★ ☆ add sparkle effects
• Combine characters for texture
• Start simple, add details gradually
• Use symmetry for balanced designs

What would you like to create?`,
      };
    } else if (
      lowerMessage.includes("mountain") ||
      lowerMessage.includes("hills")
    ) {
      response = {
        message: "Here are some mountains!",
        asciiArt: `    /\\    
   /  \\   
  /    \\  
 /      \\ 
/        \\
~~~~~~~~~~`,
      };
    } else {
      // For other requests, provide helpful guidance
      response = {
        message: `I can help you create ASCII art! Try asking me to make:
• Animals (cat, dog, bird)
• Shapes (heart, star, diamond)
• Objects (house, tree, flower)
• Or ask for tips and techniques

What would you like me to create for you?`,
      };
    }

    return Response.json(response);
  } catch (error) {
    console.error("AI Assistant Error:", error);
    return Response.json(
      {
        error: "Failed to process AI request",
      },
      { status: 500 },
    );
  }
}
